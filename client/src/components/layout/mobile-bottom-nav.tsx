import { Link, useLocation } from "wouter";
import { LayoutDashboard, Receipt, ShoppingBag, FileText } from "lucide-react";
import { cn } from "@/lib/utils";

const navigation = [
  { name: "Dashboard", href: "/dashboard", icon: LayoutDashboard },
  { name: "<PERSON>vi<PERSON><PERSON><PERSON>", href: "/transactions", icon: Receipt },
  { name: "Gastos", href: "/expenses", icon: ShoppingBag },
  { name: "Nó<PERSON>", href: "/payroll", icon: FileText },
];

export default function MobileBottomNav() {
  const [location] = useLocation();

  return (
    <nav className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
      <div className="flex items-center justify-around">
        {navigation.map((item) => {
          const isActive = location === item.href || (item.href === "/dashboard" && location === "/");
          return (
            <Link key={item.name} href={item.href} className={cn("nav-item", isActive && "active")}>
              <item.icon className={cn(
                "w-5 h-5",
                isActive ? "text-primary-foreground" : "text-muted-foreground"
              )} />
              <span className="text-xs mt-1">{item.name}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}
