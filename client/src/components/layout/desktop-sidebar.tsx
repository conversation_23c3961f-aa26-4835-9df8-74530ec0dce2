import { Link, useLocation } from "wouter";
import { DollarSign, LayoutDashboard, Receipt, ShoppingBag, FileText, User } from "lucide-react";
import { cn } from "@/lib/utils";

const navigation = [
  { name: "Dashboard", href: "/dashboard", icon: LayoutDashboard },
  { name: "Movimiento<PERSON>", href: "/transactions", icon: Receipt },
  { name: "Gastos Detallados", href: "/expenses", icon: ShoppingBag },
  { name: "Informes de Nómina", href: "/payroll", icon: FileText },
];

export default function DesktopSidebar() {
  const [location] = useLocation();

  return (
    <aside className="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 lg:bg-white lg:border-r lg:border-gray-200">
      <div className="flex items-center px-6 py-4 border-b border-gray-200">
        <div className="w-10 h-10 bg-primary rounded-xl flex items-center justify-center">
          <DollarSign className="w-6 h-6 text-primary-foreground" />
        </div>
        <div className="ml-3">
          <h1 className="text-lg font-semibold text-gray-900">Misiones</h1>
          <p className="text-sm text-gray-500">Financieras</p>
        </div>
      </div>
      
      <nav className="flex-1 px-4 py-4 space-y-1">
        {navigation.map((item) => {
          const isActive = location === item.href || (item.href === "/dashboard" && location === "/");
          return (
            <Link key={item.name} href={item.href} className={cn(
              "sidebar-link",
              isActive && "active"
            )}>
              <item.icon className={cn(
                "w-5 h-5 mr-3",
                isActive ? "text-primary-foreground" : "text-muted-foreground group-hover:text-foreground"
              )} />
              {item.name}
            </Link>
          );
        })}
      </nav>
      
      <div className="px-4 py-4 border-t border-gray-200">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-gray-600" />
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-700">Usuario</p>
            <p className="text-xs text-gray-500">Misión 4 activa</p>
          </div>
        </div>
      </div>
    </aside>
  );
}
