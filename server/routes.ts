import type { Express } from "express";
import { createServer, type Server } from "http";
import multer from "multer";
import { storage } from "./storage";
import { PayrollProcessorV2 } from "./payroll-processor-v2";
import { 
  insertTransactionSchema, 
  insertExpenseSchema,
  insertPayrollReportSchema,
  insertEmployeeSchema,
  insertPayrollConceptSchema,
  insertPayrollEntrySchema
} from "@shared/schema";
import { z } from "zod";

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedExtensions = ['.csv', '.xlsx', '.xls', '.json', '.xml', '.md'];
    const fileExtension = '.' + file.originalname.split('.').pop()?.toLowerCase();
    
    if (allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('Formato de archivo no permitido'));
    }
  }
});

export async function registerRoutes(app: Express): Promise<Server> {
  // Missions
  app.get("/api/missions", async (req, res) => {
    try {
      const missions = await storage.getMissions();
      res.json(missions);
    } catch (error) {
      console.error("Error fetching missions:", error);
      res.status(500).json({ error: "Failed to fetch missions" });
    }
  });

  app.get("/api/missions/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const mission = await storage.getMissionWithRelations(id);
      if (!mission) {
        return res.status(404).json({ error: "Mission not found" });
      }
      res.json(mission);
    } catch (error) {
      console.error("Error fetching mission:", error);
      res.status(500).json({ error: "Failed to fetch mission" });
    }
  });

  // Transactions
  app.get("/api/transactions", async (req, res) => {
    try {
      const missionId = req.query.missionId ? parseInt(req.query.missionId as string) : undefined;
      const transactions = missionId 
        ? await storage.getTransactionsByMission(missionId)
        : await storage.getTransactions();
      res.json(transactions);
    } catch (error) {
      console.error("Error fetching transactions:", error);
      res.status(500).json({ error: "Failed to fetch transactions" });
    }
  });

  app.post("/api/transactions", async (req, res) => {
    try {
      const validatedData = insertTransactionSchema.parse(req.body);
      
      // Calculate resulting balance
      const currentBalance = await storage.getCurrentBalance();
      const newBalance = validatedData.type === 'savings' 
        ? currentBalance + validatedData.amount
        : currentBalance - validatedData.amount;
      
      const transaction = await storage.createTransaction({
        ...validatedData,
        resultingBalance: newBalance,
      });
      
      res.status(201).json(transaction);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      console.error("Error creating transaction:", error);
      res.status(500).json({ error: "Failed to create transaction" });
    }
  });

  app.delete("/api/transactions/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteTransaction(id);
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting transaction:", error);
      res.status(500).json({ error: "Failed to delete transaction" });
    }
  });

  // Expense Categories
  app.get("/api/expense-categories", async (req, res) => {
    try {
      const missionId = req.query.missionId ? parseInt(req.query.missionId as string) : undefined;
      const categories = missionId 
        ? await storage.getExpenseCategoriesByMission(missionId)
        : await storage.getExpenseCategories();
      res.json(categories);
    } catch (error) {
      console.error("Error fetching expense categories:", error);
      res.status(500).json({ error: "Failed to fetch expense categories" });
    }
  });

  // Expenses
  app.get("/api/expenses", async (req, res) => {
    try {
      const categoryId = req.query.categoryId ? parseInt(req.query.categoryId as string) : undefined;
      const expenses = categoryId 
        ? await storage.getExpensesByCategory(categoryId)
        : await storage.getExpenses();
      res.json(expenses);
    } catch (error) {
      console.error("Error fetching expenses:", error);
      res.status(500).json({ error: "Failed to fetch expenses" });
    }
  });

  app.post("/api/expenses", async (req, res) => {
    try {
      const validatedData = insertExpenseSchema.parse(req.body);
      const expense = await storage.createExpense(validatedData);
      res.status(201).json(expense);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      console.error("Error creating expense:", error);
      res.status(500).json({ error: "Failed to create expense" });
    }
  });

  app.delete("/api/expenses/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteExpense(id);
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting expense:", error);
      res.status(500).json({ error: "Failed to delete expense" });
    }
  });

  // Analytics
  app.get("/api/analytics/balance", async (req, res) => {
    try {
      const currentBalance = await storage.getCurrentBalance();
      const totalSavings = await storage.getTotalSavings();
      const halfBalance = currentBalance / 2;
      
      res.json({
        currentBalance,
        totalSavings,
        halfBalance,
      });
    } catch (error) {
      console.error("Error fetching balance analytics:", error);
      res.status(500).json({ error: "Failed to fetch balance analytics" });
    }
  });

  app.get("/api/analytics/balance-history", async (req, res) => {
    try {
      const months = parseInt(req.query.months as string) || 12;
      const history = await storage.getBalanceHistory(months);
      res.json(history);
    } catch (error) {
      console.error("Error fetching balance history:", error);
      res.status(500).json({ error: "Failed to fetch balance history" });
    }
  });

  // Payroll Reports
  app.get("/api/payroll-reports", async (req, res) => {
    try {
      const reports = await storage.getPayrollReports();
      res.json(reports);
    } catch (error) {
      console.error("Error fetching payroll reports:", error);
      res.status(500).json({ error: "Failed to fetch payroll reports" });
    }
  });

  app.get("/api/payroll-reports/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const report = await storage.getPayrollReportById(id);
      if (!report) {
        return res.status(404).json({ error: "Payroll report not found" });
      }
      res.json(report);
    } catch (error) {
      console.error("Error fetching payroll report:", error);
      res.status(500).json({ error: "Failed to fetch payroll report" });
    }
  });

  app.post("/api/payroll-reports", async (req, res) => {
    try {
      const validatedData = insertPayrollReportSchema.parse(req.body);
      const report = await storage.createPayrollReport(validatedData);
      res.status(201).json(report);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      console.error("Error creating payroll report:", error);
      res.status(500).json({ error: "Failed to create payroll report" });
    }
  });

  app.delete("/api/payroll-reports/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deletePayrollReport(id);
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting payroll report:", error);
      res.status(500).json({ error: "Failed to delete payroll report" });
    }
  });

  // Employees
  app.get("/api/employees", async (req, res) => {
    try {
      const employees = await storage.getEmployees();
      res.json(employees);
    } catch (error) {
      console.error("Error fetching employees:", error);
      res.status(500).json({ error: "Failed to fetch employees" });
    }
  });

  app.post("/api/employees", async (req, res) => {
    try {
      const validatedData = insertEmployeeSchema.parse(req.body);
      const employee = await storage.createEmployee(validatedData);
      res.status(201).json(employee);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      console.error("Error creating employee:", error);
      res.status(500).json({ error: "Failed to create employee" });
    }
  });

  // Payroll Concepts
  app.get("/api/payroll-concepts", async (req, res) => {
    try {
      const section = req.query.section as string;
      const concepts = section 
        ? await storage.getPayrollConceptsBySection(section)
        : await storage.getPayrollConcepts();
      res.json(concepts);
    } catch (error) {
      console.error("Error fetching payroll concepts:", error);
      res.status(500).json({ error: "Failed to fetch payroll concepts" });
    }
  });

  app.post("/api/payroll-concepts", async (req, res) => {
    try {
      const validatedData = insertPayrollConceptSchema.parse(req.body);
      const concept = await storage.createPayrollConcept(validatedData);
      res.status(201).json(concept);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      console.error("Error creating payroll concept:", error);
      res.status(500).json({ error: "Failed to create payroll concept" });
    }
  });

  // Payroll File Import
  app.post("/api/payroll/import", upload.single('file'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No file uploaded" });
      }

      // Process the uploaded file
      const payrollData = await PayrollProcessorV2.processFile(req.file.buffer, req.file.originalname);

      // Create or find employee
      let employee = await storage.getEmployees().then(employees => 
        employees.find(emp => emp.ci === payrollData.employee.ci)
      );
      
      if (!employee) {
        employee = await storage.createEmployee(payrollData.employee);
      }

      // Create payroll report
      const report = await storage.createPayrollReport(payrollData.report);

      // Create or find concepts and entries
      const entries = [];
      for (const entryData of payrollData.entries) {
        let concept = await storage.getPayrollConcepts().then(concepts =>
          concepts.find(c => c.name === entryData.concept.name && c.section === entryData.concept.section)
        );
        
        if (!concept) {
          concept = await storage.createPayrollConcept(entryData.concept);
        }

        const entry = await storage.createPayrollEntry({
          ...entryData.entry,
          reportId: report.id,
          employeeId: employee.id,
          conceptId: concept.id
        });
        
        entries.push(entry);
      }

      res.status(201).json({
        report,
        employee,
        entriesCreated: entries.length,
        message: "Archivo de nómina importado exitosamente"
      });

    } catch (error) {
      console.error("Error importing payroll file:", error);
      
      // Send more specific error information
      const errorMessage = error instanceof Error ? error.message : "Error desconocido";
      const statusCode = errorMessage.includes('No se pudo encontrar') ? 400 : 500;
      
      res.status(statusCode).json({ 
        error: "Error al importar archivo de nómina",
        details: errorMessage,
        suggestion: statusCode === 400 ? 
          "Verifique que el archivo tenga el formato correcto con las tablas de empleado y conceptos" :
          "Intente nuevamente o contacte al administrador"
      });
    }
  });

  // Payroll Export
  app.get("/api/payroll/export/:reportId/:format", async (req, res) => {
    try {
      const reportId = parseInt(req.params.reportId);
      const format = req.params.format as 'csv' | 'xlsx' | 'json' | 'xml' | 'md';

      if (!['csv', 'xlsx', 'json', 'xml', 'md'].includes(format)) {
        return res.status(400).json({ error: "Invalid export format" });
      }

      const report = await storage.getPayrollReportById(reportId);
      if (!report) {
        return res.status(404).json({ error: "Payroll report not found" });
      }

      // Convert to PayrollData format for export
      const employee = report.entries[0]?.employee;
      if (!employee) {
        return res.status(400).json({ error: "No employee data found for this report" });
      }

      const payrollData = {
        report: {
          year: report.year,
          month: report.month,
          exchangeRate: report.exchangeRate
        },
        employee: {
          brigade: employee.brigade,
          fileNumber: employee.fileNumber,
          name: employee.name,
          ci: employee.ci,
          signature: employee.signature || '',
          costCenter: employee.costCenter,
          organizationalUnit: employee.organizationalUnit
        },
        entries: report.entries.map(entry => ({
          concept: {
            name: entry.concept.name,
            section: entry.concept.section,
            isActive: entry.concept.isActive,
            displayOrder: entry.concept.displayOrder
          },
          entry: {
            amountKz: entry.amountKz,
            amountUsd: entry.amountUsd,
            exchangeRate: entry.exchangeRate
          }
        }))
      };

      const exportBuffer = await PayrollProcessorV2.generateExport(payrollData, format);

      // Set appropriate headers
      const filename = `payroll-${report.year}-${report.month}.${format}`;
      const mimeTypes = {
        csv: 'text/csv',
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        json: 'application/json',
        xml: 'application/xml',
        md: 'text/markdown'
      };

      res.setHeader('Content-Type', mimeTypes[format]);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.send(exportBuffer);

    } catch (error) {
      console.error("Error exporting payroll data:", error);
      res.status(500).json({ 
        error: "Failed to export payroll data",
        details: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
