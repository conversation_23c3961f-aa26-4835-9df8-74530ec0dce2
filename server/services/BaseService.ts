import { logger, appLogger, errorLogger } from '../utils/logger.js';

/**
 * Base service class implementing common patterns
 * Follows Single Responsibility Principle and provides common functionality
 */
export abstract class BaseService {
  public readonly serviceName: string;

  constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  /**
   * Execute operation with logging and error handling
   * Implements common cross-cutting concerns
   */
  protected async executeOperation<T>(
    operation: () => Promise<T>,
    operationName: string,
    context?: any
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      logger.debug(`${this.serviceName}: Starting ${operationName}`, context);
      
      const result = await operation();
      
      const duration = Date.now() - startTime;
      logger.debug(`${this.serviceName}: Completed ${operationName}`, {
        ...context,
        duration,
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      errorLogger.logError(error as Error, {
        service: this.serviceName,
        operation: operationName,
        context,
        duration,
      });
      
      throw error;
    }
  }

  /**
   * Validate input data
   * Can be overridden by specific services
   */
  protected validateInput(data: any, schema?: any): void {
    if (schema) {
      schema.parse(data);
    }
  }

  /**
   * Log business events
   */
  protected logBusinessEvent(event: string, data: any): void {
    logger.info(`${this.serviceName}: ${event}`, {
      service: this.serviceName,
      event,
      data,
      timestamp: new Date().toISOString(),
    });
  }
}

/**
 * Service interface for dependency injection
 */
export interface IService {
  serviceName: string;
}

/**
 * Repository interface for data access
 */
export interface IRepository<T, CreateT = Partial<T>, UpdateT = Partial<T>> {
  findAll(): Promise<T[]>;
  findById(id: number): Promise<T | null>;
  create(data: CreateT): Promise<T>;
  update(id: number, data: UpdateT): Promise<T>;
  delete(id: number): Promise<void>;
}

/**
 * Generic CRUD service implementing common operations
 * Follows Open/Closed Principle - open for extension, closed for modification
 */
export abstract class CrudService<T, CreateT = Partial<T>, UpdateT = Partial<T>>
  extends BaseService
  implements IService {

  protected abstract repository: IRepository<T, CreateT, UpdateT>;

  /**
   * Get all entities with optional filtering
   */
  async findAll(filters?: any): Promise<T[]> {
    return this.executeOperation(
      () => this.repository.findAll(),
      'findAll',
      { filters }
    );
  }

  /**
   * Get entity by ID
   */
  async findById(id: number): Promise<T | null> {
    return this.executeOperation(
      () => this.repository.findById(id),
      'findById',
      { id }
    );
  }

  /**
   * Create new entity
   */
  async create(data: CreateT): Promise<T> {
    return this.executeOperation(
      async () => {
        this.validateInput(data, this.getCreateSchema());
        const result = await this.repository.create(data);
        this.logBusinessEvent('entity_created', { id: (result as any).id });
        return result;
      },
      'create',
      { data }
    );
  }

  /**
   * Update existing entity
   */
  async update(id: number, data: UpdateT): Promise<T> {
    return this.executeOperation(
      async () => {
        this.validateInput(data, this.getUpdateSchema());
        const result = await this.repository.update(id, data);
        this.logBusinessEvent('entity_updated', { id });
        return result;
      },
      'update',
      { id, data }
    );
  }

  /**
   * Delete entity
   */
  async delete(id: number): Promise<void> {
    return this.executeOperation(
      async () => {
        await this.repository.delete(id);
        this.logBusinessEvent('entity_deleted', { id });
      },
      'delete',
      { id }
    );
  }

  /**
   * Get entity by ID or throw error
   */
  async findByIdOrThrow(id: number): Promise<T> {
    const entity = await this.findById(id);
    if (!entity) {
      throw new Error(`${this.serviceName} with ID ${id} not found`);
    }
    return entity;
  }

  /**
   * Check if entity exists
   */
  async exists(id: number): Promise<boolean> {
    const entity = await this.findById(id);
    return entity !== null;
  }

  /**
   * Get validation schema for create operations
   * Should be implemented by concrete services
   */
  protected getCreateSchema(): any {
    return null;
  }

  /**
   * Get validation schema for update operations
   * Should be implemented by concrete services
   */
  protected getUpdateSchema(): any {
    return null;
  }
}

/**
 * Service factory interface for dependency injection
 */
export interface IServiceFactory {
  createService<T extends IService>(serviceType: new (...args: any[]) => T): T;
}

/**
 * Simple service factory implementation
 */
export class ServiceFactory implements IServiceFactory {
  private services = new Map<string, IService>();

  createService<T extends IService>(serviceType: new (...args: any[]) => T): T {
    const serviceName = serviceType.name;
    
    if (!this.services.has(serviceName)) {
      const service = new serviceType();
      this.services.set(serviceName, service);
    }
    
    return this.services.get(serviceName) as T;
  }

  /**
   * Register a service instance
   */
  registerService<T extends IService>(service: T): void {
    this.services.set(service.serviceName, service);
  }

  /**
   * Get registered service
   */
  getService<T extends IService>(serviceName: string): T {
    const service = this.services.get(serviceName);
    if (!service) {
      throw new Error(`Service ${serviceName} not found`);
    }
    return service as T;
  }
}

/**
 * Global service factory instance
 */
export const serviceFactory = new ServiceFactory();
