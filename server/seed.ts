import { db } from "./db";
import { missions, transactions, expenseCategories, expenses, payrollReports, employees, payrollConcepts, payrollEntries } from "@shared/schema";

const csvData = [
  // Mission 1 (Course 1)
  { course: 1, date: "2021-10", type: "discount", amount: 10.00, balance: -10.00 },
  { course: 1, date: "2021-11", type: "savings", amount: 480.00, balance: 470.00 },
  { course: 1, date: "2021-12", type: "savings", amount: 331.00, balance: 801.00 },
  { course: 1, date: "2022-01", type: "savings", amount: 321.00, balance: 1122.00 },
  { course: 1, date: "2022-02", type: "savings", amount: 331.00, balance: 1453.00 },
  { course: 1, date: "2022-03", type: "savings", amount: 331.00, balance: 1784.00 },
  { course: 1, date: "2022-04", type: "savings", amount: 331.00, balance: 2115.00 },
  { course: 1, date: "2022-05", type: "savings", amount: 331.00, balance: 2446.00 },
  { course: 1, date: "2022-06", type: "savings", amount: 331.00, balance: 2777.00 },
  { course: 1, date: "2022-07", type: "savings", amount: 331.00, balance: 3108.00 },
  { course: 1, date: "2022-08", type: "savings", amount: 331.00, balance: 3439.00 },
  
  // Mission 2 (Course 2)
  { course: 2, date: "2022-09", type: "savings", amount: 371.00, balance: 3810.00 },
  { course: 2, date: "2022-10", type: "savings", amount: 381.00, balance: 4191.00 },
  { course: 2, date: "2022-11", type: "savings", amount: 381.00, balance: 4572.00 },
  { course: 2, date: "2022-12", type: "savings", amount: 310.00, balance: 4882.00 },
  { course: 2, date: "2022-12", type: "discount", amount: 2440.00, balance: 2442.00 },
  { course: 2, date: "2023-02", type: "savings", amount: 308.00, balance: 2749.00 },
  { course: 2, date: "2023-03", type: "savings", amount: 331.00, balance: 3071.67 },
  { course: 2, date: "2023-04", type: "savings", amount: 331.00, balance: 3402.67 },
  { course: 2, date: "2023-05", type: "savings", amount: 321.00, balance: 3723.67 },
  { course: 2, date: "2023-06", type: "savings", amount: 431.00, balance: 4154.67 },
  { course: 2, date: "2023-07", type: "savings", amount: 431.00, balance: 4585.67 },
  { course: 2, date: "2023-08", type: "savings", amount: 335.00, balance: 4920.67 },
  { course: 2, date: "2023-09", type: "discount", amount: 2459.33, balance: 2461.34 },
  
  // Mission 3 (Course 3)
  { course: 3, date: "2023-10", type: "discount", amount: 13.75, balance: 2446.59 },
  { course: 3, date: "2023-11", type: "savings", amount: 455.00, balance: 2901.59 },
  { course: 3, date: "2023-12", type: "savings", amount: 431.00, balance: 3332.59 },
  { course: 3, date: "2024-01", type: "savings", amount: 421.00, balance: 3753.59 },
  { course: 3, date: "2024-02", type: "savings", amount: 431.00, balance: 4184.59 },
  { course: 3, date: "2024-03", type: "savings", amount: 431.00, balance: 4615.59 },
  { course: 3, date: "2024-04", type: "savings", amount: 431.00, balance: 5046.59 },
  { course: 3, date: "2024-05", type: "savings", amount: 431.00, balance: 5477.59 },
  { course: 3, date: "2024-06", type: "savings", amount: 431.00, balance: 5908.59 },
  { course: 3, date: "2024-07", type: "savings", amount: 431.00, balance: 6339.59 },
  { course: 3, date: "2024-08", type: "savings", amount: 431.00, balance: 6770.59 },
  { course: 3, date: "2024-08", type: "discount", amount: 3384.30, balance: 3386.29 },
  
  // Mission 4 (Course 4)
  { course: 4, date: "2024-09", type: "savings", amount: 385.00, balance: 3770.29 },
  { course: 4, date: "2024-10", type: "discount", amount: 13.75, balance: 3756.54 },
  { course: 4, date: "2024-11", type: "savings", amount: 308.00, balance: 4064.54 },
  { course: 4, date: "2024-12", type: "savings", amount: 331.00, balance: 4395.54 },
  { course: 4, date: "2025-01", type: "savings", amount: 321.00, balance: 4716.54 },
  { course: 4, date: "2025-02", type: "savings", amount: 331.00, balance: 5047.54 },
  { course: 4, date: "2025-03", type: "savings", amount: 331.00, balance: 5378.54 },
  { course: 4, date: "2025-04", type: "savings", amount: 331.00, balance: 5709.54 },
  { course: 4, date: "2025-05", type: "savings", amount: 331.00, balance: 6040.54 },
  { course: 4, date: "2025-06", type: "savings", amount: 331.00, balance: 6371.54 },
  { course: 4, date: "2025-07", type: "savings", amount: 331.00, balance: 6702.54 },
  { course: 4, date: "2025-08", type: "savings", amount: 331.00, balance: 7033.54 },
];

const expenseData = {
  "1ra": [
    { description: "Laptop HP Elitebook", amount: 600.00 },
    { description: "Laptop HP Elitebook", amount: 570.00 },
    { description: "Disco Duro", amount: 100.00 },
    { description: "Transferencia USD", amount: 100.00 },
  ],
  "2da": [
    { description: "Extraccion", amount: 418.00 },
    { description: "Transferencia", amount: 114.00 },
    { description: "Latas de pintura", amount: 58.00 },
    { description: "Cajita decodificadora", amount: 50.00 },
  ],
  "3ra": [
    { description: "Motos", amount: 3000.00 },
  ],
};

export async function seedDatabase() {
  try {
    console.log("Starting database seed...");

    // Clear existing data
    await db.delete(expenses);
    await db.delete(expenseCategories);
    await db.delete(transactions);
    await db.delete(missions);

    // Create missions
    const missionData = [
      { name: "Misión 1", courseNumber: 1 },
      { name: "Misión 2", courseNumber: 2 },
      { name: "Misión 3", courseNumber: 3 },
      { name: "Misión 4", courseNumber: 4 },
    ];

    const createdMissions = await db.insert(missions).values(missionData).returning();
    console.log("Created missions:", createdMissions.length);

    // Create a mapping from course number to mission ID
    const missionMap = new Map();
    createdMissions.forEach(mission => {
      missionMap.set(mission.courseNumber, mission.id);
    });

    // Create transactions
    const transactionData = csvData.map(row => ({
      date: row.date,
      type: row.type,
      amount: row.amount,
      resultingBalance: row.balance,
      missionId: missionMap.get(row.course),
    }));

    await db.insert(transactions).values(transactionData);
    console.log("Created transactions:", transactionData.length);

    // Create expense categories with funding amounts
    const categoryData = [
      { name: "1ra", fundingAmount: 2440.00, missionId: missionMap.get(1) },
      { name: "2da", fundingAmount: 2459.33, missionId: missionMap.get(2) },
      { name: "3ra", fundingAmount: 3384.30, missionId: missionMap.get(3) },
    ];

    const createdCategories = await db.insert(expenseCategories).values(categoryData).returning();
    console.log("Created expense categories:", createdCategories.length);

    // Create a mapping from category name to category ID
    const categoryMap = new Map();
    createdCategories.forEach(category => {
      categoryMap.set(category.name, category.id);
    });

    // Create expenses
    const allExpenses: Array<{
      description: string;
      amount: number;
      categoryId: number;
    }> = [];
    Object.entries(expenseData).forEach(([categoryName, expenses]) => {
      const categoryId = categoryMap.get(categoryName);
      expenses.forEach(expense => {
        allExpenses.push({
          description: expense.description,
          amount: expense.amount,
          categoryId: categoryId,
        });
      });
    });

    await db.insert(expenses).values(allExpenses);
    console.log("Created expenses:", allExpenses.length);

    // Seed sample payroll data
    console.log("Seeding payroll data...");
    
    // Clear existing payroll data
    await db.delete(payrollEntries);
    await db.delete(payrollConcepts);
    await db.delete(employees);
    await db.delete(payrollReports);

    // Create sample employee
    const [employee] = await db.insert(employees).values({
      brigade: "MES LUNDA SUL",
      fileNumber: "25950",
      name: "JAVIER REINALDO ALMARALES",
      ci: "88070736588",
      signature: "ANTEX ANGOLA S.A",
      costCenter: "MESCTI UNIVERSIDADES 2019",
      organizationalUnit: "ESCOLA SUPERIOR POLITECNICA-LUNDA SUL"
    }).returning();

    // Create payroll report for December 2024
    const [report] = await db.insert(payrollReports).values({
      year: 2024,
      month: 12,
      exchangeRate: 931.00
    }).returning();

    // Create payroll concepts
    const conceptsData = [
      { name: "SALARIO DEVENGADO", section: "ABONOS", displayOrder: 1 },
      { name: "SALARIO VUELO DE REGRESO", section: "ABONOS", displayOrder: 2 },
      { name: "DESCUENTO UJC/PCC", section: "DESCUENTOS", displayOrder: 1 },
      { name: "ENVIO A BENEFICIARIO", section: "DESCUENTOS", displayOrder: 2 },
      { name: "DESCUENTO POR DONACIONES VOLUNTARIAS", section: "DESCUENTOS", displayOrder: 3 },
      { name: "IRT", section: "DESCUENTOS", displayOrder: 4 },
      { name: "A COBRAR EN TARJETA MULTICAJA", section: "DESCUENTOS", displayOrder: 5 },
      { name: "CUENTA CUBA", section: "DESCUENTOS", displayOrder: 6 },
    ];

    const createdConcepts = await db.insert(payrollConcepts).values(conceptsData).returning();

    // Create payroll entries
    const entriesData = [
      { conceptName: "SALARIO DEVENGADO", amountKz: 0, amountUsd: 600.00 },
      { conceptName: "SALARIO VUELO DE REGRESO", amountKz: 0, amountUsd: 125.00 },
      { conceptName: "DESCUENTO UJC/PCC", amountKz: 0, amountUsd: 22.00 },
      { conceptName: "ENVIO A BENEFICIARIO", amountKz: 0, amountUsd: 51.00 },
      { conceptName: "DESCUENTO POR DONACIONES VOLUNTARIAS", amountKz: 0, amountUsd: 10.00 },
      { conceptName: "IRT", amountKz: 0, amountUsd: 0.00 },
      { conceptName: "A COBRAR EN TARJETA MULTICAJA", amountKz: 0, amountUsd: 0.00 },
      { conceptName: "A COBRAR EN TARJETA MULTICAJA", amountKz: 93100.00, amountUsd: 100.00 },
      { conceptName: "CUENTA CUBA", amountKz: 0, amountUsd: 418.00 },
      { conceptName: "CUENTA CUBA", amountKz: 0, amountUsd: 124.00 },
    ];

    const payrollEntriesData = entriesData.map(entry => {
      const concept = createdConcepts.find(c => c.name === entry.conceptName);
      return {
        reportId: report.id,
        employeeId: employee.id,
        conceptId: concept!.id,
        amountKz: entry.amountKz,
        amountUsd: entry.amountUsd,
        exchangeRate: 931.00
      };
    });

    await db.insert(payrollEntries).values(payrollEntriesData);
    console.log("Created payroll entries:", payrollEntriesData.length);

    console.log("Database seed completed successfully!");
  } catch (error) {
    console.error("Error seeding database:", error);
    throw error;
  }
}

// Run seed if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase().catch(console.error);
}
