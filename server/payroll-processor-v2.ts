// ExcelJS will be imported dynamically
import * as xml2js from 'xml2js';
import type { 
  InsertEmployee, 
  InsertPayrollConcept, 
  InsertPayrollEntry, 
  InsertPayrollReport
} from "@shared/schema";

export interface PayrollData {
  report: InsertPayrollReport;
  employee: InsertEmployee;
  entries: Array<{
    concept: InsertPayrollConcept;
    entry: Omit<InsertPayrollEntry, 'reportId' | 'employeeId' | 'conceptId'>;
  }>;
}

interface RawPayrollEntry {
  section: string;
  concept: string;
  amountKz: number;
  amountUsd: number;
  exchangeRate: number;
}

interface RawEmployeeData {
  brigade: string;
  fileNumber: string;
  name: string;
  ci: string;
  signature?: string;
  costCenter: string;
  organizationalUnit: string;
}

export class PayrollProcessorV2 {
  private static readonly SUPPORTED_EXTENSIONS = ['.csv', '.xlsx', '.xls', '.json', '.xml', '.md'];
  
  static async processFile(buffer: Buffer, filename: string): Promise<PayrollData> {
    const extension = '.' + filename.split('.').pop()?.toLowerCase();
    
    if (!this.SUPPORTED_EXTENSIONS.includes(extension)) {
      throw new Error(`Formato de archivo no soportado: ${extension}`);
    }

    let rawData: any;
    
    try {
      switch (extension) {
        case '.csv':
          rawData = await this.processCSV(buffer);
          break;
        case '.xlsx':
        case '.xls':
          rawData = await this.processExcel(buffer);
          break;
        case '.json':
          rawData = await this.processJSON(buffer);
          break;
        case '.xml':
          rawData = await this.processXML(buffer);
          break;
        case '.md':
          rawData = await this.processMarkdown(buffer);
          break;
        default:
          throw new Error(`Formato no implementado: ${extension}`);
      }
      
      return this.parsePayrollData(rawData);
    } catch (error) {
      console.error(`Error processing ${extension} file:`, error);
      throw new Error(`Error al procesar archivo ${extension}: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  private static async processCSV(buffer: Buffer): Promise<any> {
    const csvContent = buffer.toString('utf-8');
    const lines = csvContent.split('\n').map(line => line.trim()).filter(line => line);
    
    if (lines.length < 2) {
      throw new Error('El archivo CSV debe tener al menos una fila de encabezados y una fila de datos');
    }

    // Simple CSV parser
    const parseCSVLine = (line: string): string[] => {
      const result: string[] = [];
      let current = '';
      let inQuotes = false;
      
      for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          result.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      result.push(current.trim());
      return result;
    };

    const headers = parseCSVLine(lines[0]).map(h => h.toLowerCase().replace(/\s+/g, '_'));
    const rows = lines.slice(1).map(line => {
      const values = parseCSVLine(line);
      const row: any = {};
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      return row;
    });

    return this.normalizeData(rows);
  }

  private static async processExcel(buffer: Buffer): Promise<any> {
    const ExcelJS = await import('exceljs');
    const workbook = new ExcelJS.default.Workbook();
    await workbook.xlsx.load(buffer);

    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      throw new Error('El archivo Excel no contiene hojas de cálculo');
    }

    const jsonData: any[][] = [];
    worksheet.eachRow((row, rowNumber) => {
      const rowData: any[] = [];
      row.eachCell((cell, colNumber) => {
        rowData[colNumber - 1] = cell.value;
      });
      jsonData.push(rowData);
    });

    if (jsonData.length < 2) {
      throw new Error('El archivo Excel debe tener al menos una fila de encabezados y una fila de datos');
    }

    // Convert to object format
    const headers = jsonData[0].map((h: any) =>
      String(h || '').toLowerCase().replace(/\s+/g, '_')
    );
    
    const rows = jsonData.slice(1).map(row => {
      const obj: any = {};
      headers.forEach((header, index) => {
        obj[header] = row[index] || '';
      });
      return obj;
    }).filter(row => Object.values(row).some(val => val !== ''));

    return this.normalizeData(rows);
  }

  private static async processJSON(buffer: Buffer): Promise<any> {
    const jsonContent = buffer.toString('utf-8');
    const data = JSON.parse(jsonContent);
    
    if (Array.isArray(data)) {
      return this.normalizeData(data);
    } else if (typeof data === 'object') {
      // Handle different JSON structures
      if (data.entries || data.employee || data.year) {
        return data;
      } else {
        return this.normalizeData([data]);
      }
    }
    
    throw new Error('Estructura JSON no válida');
  }

  private static async processXML(buffer: Buffer): Promise<any> {
    const xmlContent = buffer.toString('utf-8');
    const parser = new xml2js.Parser({ explicitArray: false, mergeAttrs: true });
    
    const result = await new Promise((resolve, reject) => {
      parser.parseString(xmlContent, (err: any, result: any) => {
        if (err) reject(err);
        else resolve(result);
      });
    });
    
    return this.extractDataFromXML(result);
  }

  private static async processMarkdown(buffer: Buffer): Promise<any> {
    const markdown = buffer.toString('utf-8');
    const lines = markdown.split('\n');
    
    // Extract metadata
    let year = new Date().getFullYear();
    let month = new Date().getMonth() + 1;
    let exchangeRate = 931.00;
    
    const yearMatch = markdown.match(/Año:\s*(\d{4})/i);
    const monthMatch = markdown.match(/Mes:\s*(\d{1,2})/i);
    
    if (yearMatch) year = parseInt(yearMatch[1]);
    if (monthMatch) month = parseInt(monthMatch[1]);

    // Find tables
    const tables: any[][] = [];
    let currentTable: any[] = [];
    let headers: string[] = [];
    let isInTable = false;

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      if (trimmedLine.includes('|') && trimmedLine.length > 1) {
        const cells = trimmedLine.split('|')
          .map(cell => cell.trim())
          .filter(cell => cell !== '');
        
        if (!isInTable) {
          headers = cells.map(h => h.toLowerCase().replace(/\s+/g, '_'));
          isInTable = true;
        } else if (trimmedLine.includes('---')) {
          continue;
        } else if (cells.length >= headers.length) {
          const row: any = {};
          cells.forEach((cell, index) => {
            if (headers[index]) {
              row[headers[index]] = cell;
            }
          });
          currentTable.push(row);
        }
      } else if (isInTable && (trimmedLine === '' || !trimmedLine.includes('|'))) {
        if (currentTable.length > 0) {
          tables.push([...currentTable]);
          currentTable = [];
        }
        isInTable = false;
        headers = [];
      }
    }
    
    if (currentTable.length > 0) {
      tables.push(currentTable);
    }

    return {
      year,
      month,
      exchangeRate,
      tables
    };
  }

  private static normalizeData(rows: any[]): any {
    // Group rows by type (employee, concepts, etc.)
    const employeeData: any[] = [];
    const conceptData: any[] = [];
    
    for (const row of rows) {
      // Detect if this row contains employee information
      if (row.brigada || row.brigade || row.expediente || row.file_number || row.colaborador || row.name) {
        employeeData.push(row);
      }
      // Detect if this row contains concept information
      else if (row.seccion || row.section || row.concepto || row.concept) {
        conceptData.push(row);
      }
    }
    
    return {
      employeeData,
      conceptData,
      year: new Date().getFullYear(),
      month: new Date().getMonth() + 1,
      exchangeRate: 931.00
    };
  }

  private static extractDataFromXML(xmlResult: any): any {
    // Handle different XML structures
    const data: any = {
      employeeData: [],
      conceptData: [],
      year: new Date().getFullYear(),
      month: new Date().getMonth() + 1,
      exchangeRate: 931.00
    };

    if (xmlResult.payroll) {
      const payroll = xmlResult.payroll;
      
      if (payroll.metadata) {
        data.year = parseInt(payroll.metadata.year) || data.year;
        data.month = parseInt(payroll.metadata.month) || data.month;
        data.exchangeRate = parseFloat(payroll.metadata.exchangeRate) || data.exchangeRate;
      }
      
      if (payroll.employee) {
        data.employeeData.push(payroll.employee);
      }
      
      if (payroll.entries && Array.isArray(payroll.entries)) {
        data.conceptData = payroll.entries;
      }
    }

    return data;
  }

  private static parsePayrollData(rawData: any): PayrollData {
    // Handle different data structures
    let employeeData: RawEmployeeData;
    let conceptEntries: RawPayrollEntry[] = [];
    let year = rawData.year || new Date().getFullYear();
    let month = rawData.month || new Date().getMonth() + 1;
    let exchangeRate = rawData.exchangeRate || 931.00;

    // Extract employee data
    if (rawData.employee) {
      // Direct employee object
      employeeData = this.normalizeEmployee(rawData.employee);
    } else if (rawData.employeeData && rawData.employeeData.length > 0) {
      // Employee in array
      employeeData = this.normalizeEmployee(rawData.employeeData[0]);
    } else if (rawData.tables && rawData.tables.length > 0) {
      // Markdown tables
      const employeeTable = rawData.tables.find((table: any[]) => 
        table.some(row => row.brigada || row.brigade || row.expediente || row.file_number)
      );
      if (employeeTable && employeeTable.length > 0) {
        employeeData = this.normalizeEmployee(employeeTable[0]);
      } else {
        throw new Error('No se encontraron datos del empleado en las tablas');
      }
    } else {
      throw new Error('No se encontraron datos del empleado');
    }

    // Extract concept entries
    if (rawData.entries && Array.isArray(rawData.entries)) {
      conceptEntries = rawData.entries.map((entry: any) => this.normalizeConcept(entry, exchangeRate));
    } else if (rawData.conceptData && Array.isArray(rawData.conceptData)) {
      conceptEntries = rawData.conceptData.map((entry: any) => this.normalizeConcept(entry, exchangeRate));
    } else if (rawData.tables && rawData.tables.length > 1) {
      // Markdown concept table
      const conceptTable = rawData.tables.find((table: any[]) => 
        table.some(row => row.seccion || row.section || row.concepto || row.concept)
      );
      if (conceptTable) {
        conceptEntries = conceptTable.map((entry: any) => this.normalizeConcept(entry, exchangeRate));
      }
    }

    if (conceptEntries.length === 0) {
      throw new Error('No se encontraron conceptos de nómina');
    }

    // Create final structure
    const employee: InsertEmployee = {
      brigade: employeeData.brigade,
      fileNumber: employeeData.fileNumber,
      name: employeeData.name,
      ci: employeeData.ci,
      signature: employeeData.signature || '',
      costCenter: employeeData.costCenter,
      organizationalUnit: employeeData.organizationalUnit
    };

    const report: InsertPayrollReport = {
      year,
      month,
      exchangeRate
    };

    const entries = conceptEntries.map(concept => ({
      concept: {
        name: concept.concept,
        section: concept.section,
        isActive: 1,
        displayOrder: 0
      } as InsertPayrollConcept,
      entry: {
        amountKz: concept.amountKz || 0,
        amountUsd: concept.amountUsd || 0,
        exchangeRate: concept.exchangeRate || 931.00
      }
    }));

    return { report, employee, entries };
  }

  private static normalizeEmployee(rawEmployee: any): RawEmployeeData {
    return {
      brigade: rawEmployee.brigada || rawEmployee.brigade || '',
      fileNumber: rawEmployee.expediente || rawEmployee.file_number || rawEmployee.fileNumber || '',
      name: rawEmployee.colaborador || rawEmployee.name || '',
      ci: rawEmployee.ci || '',
      signature: rawEmployee.firma || rawEmployee.signature || '',
      costCenter: rawEmployee.centro_de_costo || rawEmployee.cost_center || rawEmployee.costCenter || '',
      organizationalUnit: rawEmployee.unidad_organica || rawEmployee.organizational_unit || rawEmployee.organizationalUnit || ''
    };
  }

  private static normalizeConcept(rawConcept: any, defaultExchangeRate: number): RawPayrollEntry {
    const parseAmount = (value: any): number => {
      if (typeof value === 'number') return value;
      if (typeof value === 'string') {
        // Remove currency symbols and spaces
        const cleaned = value.replace(/[$\s,]/g, '');
        const number = parseFloat(cleaned);
        return isNaN(number) ? 0 : number;
      }
      return 0;
    };

    return {
      section: rawConcept.seccion || rawConcept.section || '',
      concept: rawConcept.concepto || rawConcept.concept || '',
      amountKz: parseAmount(rawConcept.saldo_kz || rawConcept.amount_kz || rawConcept.amountKz || 0),
      amountUsd: parseAmount(rawConcept.saldo_usd || rawConcept.amount_usd || rawConcept.amountUsd || 0),
      exchangeRate: parseAmount(rawConcept.tasa_de_cambio || rawConcept.exchange_rate || rawConcept.exchangeRate || defaultExchangeRate)
    };
  }

  // Export functionality
  static async generateExport(payrollData: PayrollData, format: 'csv' | 'xlsx' | 'json' | 'xml' | 'md'): Promise<Buffer> {
    switch (format) {
      case 'csv':
        return this.generateCSV(payrollData);
      case 'xlsx':
        return await this.generateExcel(payrollData);
      case 'json':
        return this.generateJSON(payrollData);
      case 'xml':
        return this.generateXML(payrollData);
      case 'md':
        return this.generateMarkdown(payrollData);
      default:
        throw new Error(`Formato de exportación no soportado: ${format}`);
    }
  }

  private static generateCSV(data: PayrollData): Buffer {
    const rows = [
      ['Brigada', 'Expediente', 'Colaborador', 'CI', 'Firma', 'Centro de Costo', 'Unidad Orgánica'],
      [
        data.employee.brigade,
        data.employee.fileNumber,
        data.employee.name,
        data.employee.ci,
        data.employee.signature,
        data.employee.costCenter,
        data.employee.organizationalUnit
      ],
      [],
      ['Sección', 'Concepto', 'Saldo KZ', 'Saldo USD', 'Tasa de Cambio'],
      ...data.entries.map(entry => [
        entry.concept.section,
        entry.concept.name,
        (entry.entry.amountKz || 0).toString(),
        (entry.entry.amountUsd || 0).toString(),
        entry.entry.exchangeRate.toString()
      ])
    ];

    const csvContent = rows.map(row => 
      row.map(cell => `"${cell}"`).join(',')
    ).join('\n');

    return Buffer.from(csvContent, 'utf-8');
  }

  private static async generateExcel(data: PayrollData): Promise<Buffer> {
    const ExcelJS = await import('exceljs');
    const workbook = new ExcelJS.default.Workbook();

    // Employee sheet
    const employeeSheet = workbook.addWorksheet('Empleado');
    const employeeData = [
      ['Campo', 'Valor'],
      ['Brigada', data.employee.brigade],
      ['Expediente', data.employee.fileNumber],
      ['Colaborador', data.employee.name],
      ['CI', data.employee.ci],
      ['Firma', data.employee.signature],
      ['Centro de Costo', data.employee.costCenter],
      ['Unidad Orgánica', data.employee.organizationalUnit]
    ];
    employeeSheet.addRows(employeeData);

    // Concepts sheet
    const conceptsSheet = workbook.addWorksheet('Conceptos');
    const conceptsData = [
      ['Sección', 'Concepto', 'Saldo KZ', 'Saldo USD', 'Tasa de Cambio'],
      ...data.entries.map(entry => [
        entry.concept.section,
        entry.concept.name,
        entry.entry.amountKz || 0,
        entry.entry.amountUsd || 0,
        entry.entry.exchangeRate
      ])
    ];
    conceptsSheet.addRows(conceptsData);

    return Buffer.from(await workbook.xlsx.writeBuffer());
  }

  private static generateJSON(data: PayrollData): Buffer {
    const exportData = {
      year: data.report.year,
      month: data.report.month,
      exchangeRate: data.report.exchangeRate,
      employee: data.employee,
      entries: data.entries.map(entry => ({
        section: entry.concept.section,
        concept: entry.concept.name,
        amountKz: entry.entry.amountKz,
        amountUsd: entry.entry.amountUsd,
        exchangeRate: entry.entry.exchangeRate
      }))
    };

    return Buffer.from(JSON.stringify(exportData, null, 2), 'utf-8');
  }

  private static generateXML(data: PayrollData): Buffer {
    const xmlData = {
      payroll: {
        metadata: {
          year: data.report.year,
          month: data.report.month,
          exchangeRate: data.report.exchangeRate
        },
        employee: data.employee,
        entries: data.entries.map(entry => ({
          section: entry.concept.section,
          concept: entry.concept.name,
          amountKz: entry.entry.amountKz,
          amountUsd: entry.entry.amountUsd,
          exchangeRate: entry.entry.exchangeRate
        }))
      }
    };

    const builder = new xml2js.Builder({ rootName: 'payroll' });
    const xml = builder.buildObject(xmlData);
    return Buffer.from(xml, 'utf-8');
  }

  private static generateMarkdown(data: PayrollData): Buffer {
    const markdown = `## Informe de Resumen Mensual de Nómina

Año: ${data.report.year}  Mes: ${data.report.month}

| Brigada | Expediente | Colaborador | CI | Firma | Centro de Costo | Unidad Orgánica |
| --- | --- | --- | --- | --- | --- | --- |
| ${data.employee.brigade} | ${data.employee.fileNumber} | ${data.employee.name} | ${data.employee.ci} | ${data.employee.signature} | ${data.employee.costCenter} | ${data.employee.organizationalUnit} |

| Sección | Concepto | Saldo KZ | Saldo USD | Tasa de Cambio |
| --- | --- | --- | --- | --- |
${data.entries.map(entry => 
  `| ${entry.concept.section} | ${entry.concept.name} | $ ${(entry.entry.amountKz || 0).toFixed(2)} | $ ${(entry.entry.amountUsd || 0).toFixed(2)} | ${(entry.entry.exchangeRate || 931).toFixed(2)} |`
).join('\n')}`;

    return Buffer.from(markdown, 'utf-8');
  }
}