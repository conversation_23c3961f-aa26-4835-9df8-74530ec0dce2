import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TransactionService, TransactionRepository } from '@server/services/TransactionService';
import { DatabaseStorage } from '@server/storage';
import { createMockTransaction, TestDatabase } from '@tests/utils/testHelpers';

/**
 * Unit tests for TransactionService
 * Tests business logic and service layer functionality
 */

// Mock the database storage
vi.mock('@server/storage');
vi.mock('@server/utils/logger');

describe('TransactionService', () => {
  let transactionService: TransactionService;
  let mockStorage: DatabaseStorage;
  let testDb: TestDatabase;

  beforeEach(() => {
    // Reset test database
    testDb = TestDatabase.getInstance();
    testDb.reset();

    // Create mock storage
    mockStorage = {
      getTransactions: vi.fn(),
      getTransactionsByMission: vi.fn(),
      createTransaction: vi.fn(),
      deleteTransaction: vi.fn(),
      getCurrentBalance: vi.fn(),
      getTotalSavings: vi.fn(),
      getBalanceHistory: vi.fn(),
    } as any;

    // Create service instance
    transactionService = new TransactionService(mockStorage);
  });

  describe('createTransaction', () => {
    it('should create a savings transaction with correct balance calculation', async () => {
      // Arrange
      const currentBalance = 1000;
      const transactionData = {
        date: '2024-01',
        type: 'savings' as const,
        amount: 500,
        missionId: 1,
      };
      const expectedBalance = currentBalance + transactionData.amount;

      mockStorage.getCurrentBalance = vi.fn().mockResolvedValue(currentBalance);
      mockStorage.createTransaction = vi.fn().mockResolvedValue({
        id: 1,
        ...transactionData,
        resultingBalance: expectedBalance,
        createdAt: new Date(),
      });

      // Act
      const result = await transactionService.createTransaction(transactionData);

      // Assert
      expect(mockStorage.getCurrentBalance).toHaveBeenCalledOnce();
      expect(mockStorage.createTransaction).toHaveBeenCalledWith({
        ...transactionData,
        resultingBalance: expectedBalance,
      });
      expect(result.resultingBalance).toBe(expectedBalance);
    });

    it('should create a discount transaction with correct balance calculation', async () => {
      // Arrange
      const currentBalance = 1000;
      const transactionData = {
        date: '2024-01',
        type: 'discount' as const,
        amount: 300,
        missionId: 1,
      };
      const expectedBalance = currentBalance - transactionData.amount;

      mockStorage.getCurrentBalance = vi.fn().mockResolvedValue(currentBalance);
      mockStorage.createTransaction = vi.fn().mockResolvedValue({
        id: 1,
        ...transactionData,
        resultingBalance: expectedBalance,
        createdAt: new Date(),
      });

      // Act
      const result = await transactionService.createTransaction(transactionData);

      // Assert
      expect(result.resultingBalance).toBe(expectedBalance);
    });

    it('should throw error for invalid transaction data', async () => {
      // Arrange
      const invalidData = {
        date: 'invalid-date',
        type: 'invalid-type' as any,
        amount: -100, // negative amount
        missionId: 1,
      };

      // Act & Assert
      await expect(transactionService.createTransaction(invalidData))
        .rejects.toThrow();
    });

    it('should throw error for zero amount', async () => {
      // Arrange
      const invalidData = {
        date: '2024-01',
        type: 'savings' as const,
        amount: 0,
        missionId: 1,
      };

      // Act & Assert
      await expect(transactionService.createTransaction(invalidData))
        .rejects.toThrow('Transaction amount must be positive');
    });
  });

  describe('getBalanceAnalytics', () => {
    it('should return correct balance analytics', async () => {
      // Arrange
      const currentBalance = 2000;
      const totalSavings = 5000;

      mockStorage.getCurrentBalance = vi.fn().mockResolvedValue(currentBalance);
      mockStorage.getTotalSavings = vi.fn().mockResolvedValue(totalSavings);

      // Act
      const result = await transactionService.getBalanceAnalytics();

      // Assert
      expect(result).toEqual({
        currentBalance,
        totalSavings,
        halfBalance: currentBalance / 2,
      });
    });
  });

  describe('canDelete', () => {
    it('should allow deletion of recent transactions', async () => {
      // Arrange
      const currentDate = new Date();
      const recentTransaction = createMockTransaction({
        id: 1,
        date: `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`,
      });

      mockStorage.getTransactions = vi.fn().mockResolvedValue([recentTransaction]);

      // Act
      const canDelete = await transactionService.canDelete(1);

      // Assert
      expect(canDelete).toBe(true);
    });

    it('should not allow deletion of old transactions', async () => {
      // Arrange
      const oldDate = new Date();
      oldDate.setMonth(oldDate.getMonth() - 2); // 2 months ago
      const oldTransaction = createMockTransaction({
        id: 1,
        date: `${oldDate.getFullYear()}-${String(oldDate.getMonth() + 1).padStart(2, '0')}`,
      });

      mockStorage.getTransactions = vi.fn().mockResolvedValue([oldTransaction]);

      // Act
      const canDelete = await transactionService.canDelete(1);

      // Assert
      expect(canDelete).toBe(false);
    });

    it('should return false for non-existent transaction', async () => {
      // Arrange
      mockStorage.getTransactions = vi.fn().mockResolvedValue([]);

      // Act
      const canDelete = await transactionService.canDelete(999);

      // Assert
      expect(canDelete).toBe(false);
    });
  });

  describe('safeDelete', () => {
    it('should delete transaction if allowed', async () => {
      // Arrange
      const recentDate = new Date();
      const recentTransaction = createMockTransaction({
        id: 1,
        date: `${recentDate.getFullYear()}-${String(recentDate.getMonth() + 1).padStart(2, '0')}`,
      });

      mockStorage.getTransactions = vi.fn().mockResolvedValue([recentTransaction]);
      mockStorage.deleteTransaction = vi.fn().mockResolvedValue(undefined);

      // Act
      await transactionService.safeDelete(1);

      // Assert
      expect(mockStorage.deleteTransaction).toHaveBeenCalledWith(1);
    });

    it('should throw error if deletion not allowed', async () => {
      // Arrange
      const oldDate = new Date();
      oldDate.setMonth(oldDate.getMonth() - 2);
      const oldTransaction = createMockTransaction({
        id: 1,
        date: `${oldDate.getFullYear()}-${String(oldDate.getMonth() + 1).padStart(2, '0')}`,
      });

      mockStorage.getTransactions = vi.fn().mockResolvedValue([oldTransaction]);

      // Act & Assert
      await expect(transactionService.safeDelete(1))
        .rejects.toThrow('Cannot delete transaction older than one month');
    });
  });

  describe('findByMission', () => {
    it('should return transactions for specific mission', async () => {
      // Arrange
      const missionId = 1;
      const transactions = [
        createMockTransaction({ id: 1, missionId }),
        createMockTransaction({ id: 2, missionId }),
      ];

      mockStorage.getTransactionsByMission = vi.fn().mockResolvedValue(transactions);

      // Act
      const result = await transactionService.findByMission(missionId);

      // Assert
      expect(mockStorage.getTransactionsByMission).toHaveBeenCalledWith(missionId);
      expect(result).toEqual(transactions);
    });
  });

  describe('getBalanceHistory', () => {
    it('should return balance history for specified months', async () => {
      // Arrange
      const months = 6;
      const history = [
        { date: '2024-01', balance: 1000 },
        { date: '2024-02', balance: 1500 },
        { date: '2024-03', balance: 2000 },
      ];

      mockStorage.getBalanceHistory = vi.fn().mockResolvedValue(history);

      // Act
      const result = await transactionService.getBalanceHistory(months);

      // Assert
      expect(mockStorage.getBalanceHistory).toHaveBeenCalledWith(months);
      expect(result).toEqual(history);
    });

    it('should use default months if not specified', async () => {
      // Arrange
      const defaultMonths = 12;
      const history = [{ date: '2024-01', balance: 1000 }];

      mockStorage.getBalanceHistory = vi.fn().mockResolvedValue(history);

      // Act
      await transactionService.getBalanceHistory();

      // Assert
      expect(mockStorage.getBalanceHistory).toHaveBeenCalledWith(defaultMonths);
    });
  });
});
