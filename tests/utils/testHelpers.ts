import { vi } from 'vitest';
import { render, RenderOptions } from '@testing-library/react';
import React, { ReactElement, ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TooltipProvider } from '@/components/ui/tooltip';

/**
 * Test utilities and helpers
 * Provides common testing functionality following DRY principles
 */

/**
 * Create a test query client with default configuration
 */
export function createTestQueryClient(): QueryClient {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
}

/**
 * Test wrapper component with providers
 */
interface TestWrapperProps {
  children: ReactNode;
  queryClient?: QueryClient;
}

function TestWrapper({ children, queryClient }: TestWrapperProps) {
  const client = queryClient || createTestQueryClient();

  return React.createElement(
    QueryClientProvider,
    { client },
    React.createElement(TooltipProvider, { children })
  );
}

/**
 * Custom render function with providers
 */
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient;
}

export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { queryClient, ...renderOptions } = options;
  
  return render(ui, {
    wrapper: ({ children }) => React.createElement(
      TestWrapper,
      { queryClient, children }
    ),
    ...renderOptions,
  });
}

/**
 * Mock API response helper
 */
export function mockApiResponse<T>(data: T, status = 200) {
  return {
    ok: status >= 200 && status < 300,
    status,
    json: vi.fn().mockResolvedValue(data),
    text: vi.fn().mockResolvedValue(JSON.stringify(data)),
    headers: new Headers(),
    statusText: status === 200 ? 'OK' : 'Error',
  } as unknown as Response;
}

/**
 * Mock fetch with specific responses
 */
export function mockFetch(responses: Record<string, any>) {
  const mockFetch = vi.fn();
  
  Object.entries(responses).forEach(([url, response]) => {
    mockFetch.mockImplementation((requestUrl: string) => {
      if (requestUrl.includes(url)) {
        return Promise.resolve(mockApiResponse(response));
      }
      return Promise.reject(new Error(`No mock response for ${requestUrl}`));
    });
  });
  
  global.fetch = mockFetch;
  return mockFetch;
}

/**
 * Create mock user for authentication tests
 */
export function createMockUser(overrides = {}) {
  return {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'readonly',
    isActive: true,
    createdAt: new Date(),
    ...overrides,
  };
}

/**
 * Create mock transaction data
 */
export function createMockTransaction(overrides = {}) {
  return {
    id: 1,
    date: '2024-01',
    type: 'savings',
    amount: 1000,
    resultingBalance: 5000,
    missionId: 1,
    createdAt: new Date(),
    ...overrides,
  };
}

/**
 * Create mock employee data
 */
export function createMockEmployee(overrides = {}) {
  return {
    id: 1,
    brigade: 'Test Brigade',
    fileNumber: 'EMP001',
    name: 'John Doe',
    ci: '12345678',
    signature: 'J. Doe',
    costCenter: 'CC001',
    organizationalUnit: 'Test Unit',
    createdAt: new Date(),
    ...overrides,
  };
}

/**
 * Create mock payroll report data
 */
export function createMockPayrollReport(overrides = {}) {
  return {
    id: 1,
    year: 2024,
    month: 1,
    exchangeRate: 931.0,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  };
}

/**
 * Wait for async operations to complete
 */
export function waitForAsync(ms = 0) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Mock timer utilities
 */
export function mockTimers() {
  vi.useFakeTimers();
  return {
    advanceTime: (ms: number) => vi.advanceTimersByTime(ms),
    runAllTimers: () => vi.runAllTimers(),
    restore: () => vi.useRealTimers(),
  };
}

/**
 * Mock local storage
 */
export function mockLocalStorage() {
  const store: Record<string, string> = {};
  
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    }),
    get store() {
      return { ...store };
    },
  };
}

/**
 * Mock console methods
 */
export function mockConsole() {
  const originalConsole = { ...console };
  
  return {
    log: vi.spyOn(console, 'log').mockImplementation(() => {}),
    error: vi.spyOn(console, 'error').mockImplementation(() => {}),
    warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
    info: vi.spyOn(console, 'info').mockImplementation(() => {}),
    debug: vi.spyOn(console, 'debug').mockImplementation(() => {}),
    restore: () => {
      Object.assign(console, originalConsole);
    },
  };
}

/**
 * Create mock Express request object
 */
export function createMockRequest(overrides = {}) {
  return {
    method: 'GET',
    url: '/test',
    path: '/test',
    headers: {},
    query: {},
    params: {},
    body: {},
    ip: '127.0.0.1',
    get: vi.fn(),
    ...overrides,
  };
}

/**
 * Create mock Express response object
 */
export function createMockResponse() {
  const res = {
    status: vi.fn().mockReturnThis(),
    json: vi.fn().mockReturnThis(),
    send: vi.fn().mockReturnThis(),
    end: vi.fn().mockReturnThis(),
    setHeader: vi.fn().mockReturnThis(),
    getHeader: vi.fn(),
    removeHeader: vi.fn().mockReturnThis(),
    cookie: vi.fn().mockReturnThis(),
    clearCookie: vi.fn().mockReturnThis(),
    redirect: vi.fn().mockReturnThis(),
    locals: {},
    headersSent: false,
    statusCode: 200,
  };
  
  return res;
}

/**
 * Create mock Express next function
 */
export function createMockNext() {
  return vi.fn();
}

/**
 * Test database utilities
 */
export class TestDatabase {
  private static instance: TestDatabase;
  private data: Map<string, any[]> = new Map();
  
  static getInstance(): TestDatabase {
    if (!TestDatabase.instance) {
      TestDatabase.instance = new TestDatabase();
    }
    return TestDatabase.instance;
  }
  
  seed(table: string, data: any[]): void {
    this.data.set(table, [...data]);
  }
  
  get(table: string): any[] {
    return this.data.get(table) || [];
  }
  
  insert(table: string, record: any): any {
    const records = this.get(table);
    const newRecord = { id: records.length + 1, ...record };
    records.push(newRecord);
    this.data.set(table, records);
    return newRecord;
  }
  
  update(table: string, id: number, updates: any): any {
    const records = this.get(table);
    const index = records.findIndex(r => r.id === id);
    if (index === -1) throw new Error(`Record with id ${id} not found`);
    
    records[index] = { ...records[index], ...updates };
    return records[index];
  }
  
  delete(table: string, id: number): void {
    const records = this.get(table);
    const index = records.findIndex(r => r.id === id);
    if (index === -1) throw new Error(`Record with id ${id} not found`);
    
    records.splice(index, 1);
  }
  
  clear(): void {
    this.data.clear();
  }
  
  reset(): void {
    this.clear();
  }
}

/**
 * Export commonly used testing utilities
 */
export * from '@testing-library/react';
export * from '@testing-library/user-event';
export { vi } from 'vitest';
