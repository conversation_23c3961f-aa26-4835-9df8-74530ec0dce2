{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "NODE_ENV=development tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "playwright:install": "playwright install", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "docker:build": "docker build -t webflowmaster .", "docker:dev": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up", "docker:prod": "docker-compose up", "docker:down": "docker-compose down", "docker:clean": "docker-compose down -v --remove-orphans"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@supabase/supabase-js": "^2.39.0", "@tanstack/react-query": "^5.60.5", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/papaparse": "^5.3.16", "@types/xml2js": "^0.4.14", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^17.0.1", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "exceljs": "^4.4.0", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "helmet": "^8.1.0", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "multer": "^2.0.1", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "passport": "^0.7.0", "passport-local": "^1.0.0", "postgres": "^3.4.3", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "winston": "^3.17.0", "wouter": "^3.3.5", "ws": "^8.18.0", "xml2js": "^0.6.2", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@playwright/test": "^1.53.2", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.19", "@types/exceljs": "^0.5.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/supertest": "^6.0.3", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-react": "^4.3.2", "@vitejs/plugin-react-swc": "^3.10.2", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "eslint": "^8.57.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-security": "^3.0.1", "eslint-plugin-vitest": "^0.5.4", "jsdom": "^26.1.0", "playwright": "^1.53.2", "postcss": "^8.4.47", "prettier": "^3.6.2", "reflect-metadata": "^0.2.2", "supertest": "^7.1.1", "tailwindcss": "^3.4.17", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.19", "vite-plugin-eslint": "^1.8.1", "vitest": "^3.2.4"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}