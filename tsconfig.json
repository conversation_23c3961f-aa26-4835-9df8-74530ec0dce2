{"include": ["client/src/**/*", "shared/**/*", "server/**/*", "tests/**/*"], "exclude": ["node_modules", "build", "dist"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "module": "ESNext", "strict": true, "lib": ["esnext", "dom", "dom.iterable"], "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "allowImportingTsExtensions": true, "moduleResolution": "bundler", "baseUrl": ".", "types": ["node", "vite/client"], "downlevelIteration": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "paths": {"@/*": ["./client/src/*"], "@shared/*": ["./shared/*"], "@server/*": ["./server/*"], "@tests/*": ["./tests/*"]}}}